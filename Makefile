# Makefile for Svelte 5 + SvelteKit + shadcn-svelte Docker operations
DC=docker compose
.PHONY: help dev prod build-dev build-prod up-dev up-prod down-dev down-dev logs-dev logs-prod clean

# Default target
help:
	@echo "Available commands:"
	@echo "  build-dev    - Build development image"
	@echo "  build-prod   - Build production image"
	@echo "  up-dev       - Start development services"
	@echo "  up-prod      - Start production services"
	@echo "  down-dev     - Stop development services"
	@echo "  down-dev    - Stop production services"
	@echo "  logs-dev     - View development logs"
	@echo "  logs-prod    - View production logs"
	@echo "  clean        - Remove all containers and images"

build-dev:
	${DC} -f docker-compose.dev.yml build

up-dev: build-dev
	${DC} -f docker-compose.dev.yml up

down-dev:
	${DC} -f docker-compose.dev.yml down

logs-dev:
	${DC} -f docker-compose.dev.yml logs -f

build-prod:
	${DC} -f docker-compose.prod.yml build

up-prod: build-prod
	${DC} -f docker-compose.prod.yml up -d

down-prod:
	${DC} -f docker-compose.prod.yml down

logs-prod:
	${DC} -f docker-compose.prod.yml logs -f

# Cleanup
clean:
	${DC} -f docker-compose.dev.yml down --rmi all --volumes --remove-orphans
	${DC} -f docker-compose.prod.yml down --rmi all --volumes --remove-orphans
	docker system prune -f
